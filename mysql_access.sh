#!/bin/bash

# 配置路径
LOG_DIR="/opt/mysql/logs"
STATE_DIR="/opt/mysql/monitor_state"
EVENT_LOG="$LOG_DIR/mysql_check.log"
DEBUG_LOG="/tmp/mysql_monitor_debug.log"

ERROR_LOG="$LOG_DIR/error.log"
GENERAL_LOG="$LOG_DIR/general.log"

# 阈值与时间窗口
FAIL_THRESHOLD=3
SUCCESS_THRESHOLD=15
TIME_WINDOW=3600  # 秒 (1小时，更合理的监控窗口)

# 白名单（可填网段/IP）
IP_WHITELIST=( "127.0.0.1" "::1" )  # 移除空字符串，添加本地回环地址

# 检查依赖命令
check_dependencies() {
    local missing_deps=()

    command -v ipcalc >/dev/null 2>&1 || missing_deps+=("ipcalc")
    command -v getent >/dev/null 2>&1 || missing_deps+=("getent")

    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo "ERROR: Missing required commands: ${missing_deps[*]}" >&2
        echo "Please install missing dependencies and try again." >&2
        exit 1
    fi
}

# 创建所需目录和日志文件
create_directories() {
    if ! mkdir -p "$LOG_DIR" "$STATE_DIR"; then
        echo "ERROR: Failed to create directories" >&2
        exit 1
    fi

    # 检查日志文件是否可读
    for log_file in "$ERROR_LOG" "$GENERAL_LOG"; do
        if [ ! -r "$log_file" ]; then
            echo "WARNING: Cannot read $log_file" >&2
        fi
    done

    # 创建事件日志和调试日志
    touch "$EVENT_LOG" "$DEBUG_LOG" 2>/dev/null || {
        echo "ERROR: Failed to create log files" >&2
        exit 1
    }
}

# 获取文件大小
get_current_size() {
    [ -f "$1" ] && stat -c %s "$1" || echo 0
}

# 记录已读位置
record_position() {
    echo $2 > "$STATE_DIR/$(basename $1).pos"
}

# 获取上次已读位置
get_last_position() {
    local pos_file="$STATE_DIR/$(basename $1).pos"
    [ -f "$pos_file" ] && cat "$pos_file" || echo 0
}

# 判断 IP 是否在白名单
is_whitelisted() {
    local ip=$1

    # 检查输入是否为空
    [[ -z "$ip" ]] && return 1

    for subnet in "${IP_WHITELIST[@]}"; do
        [[ -z "$subnet" ]] && continue

        if [[ "$subnet" == *"/"* ]]; then
            # 网段匹配，增加错误处理
            IFS='/' read -r net mask <<< "$subnet"
            local network_result
            network_result=$(ipcalc -n "$ip/$mask" 2>/dev/null | awk -F= '/NETWORK/ {print $2}')
            if [[ -n "$network_result" && "$network_result" == "$net" ]]; then
                echo "DEBUG [WHITELIST] IP $ip matched subnet $subnet" >> "$DEBUG_LOG"
                return 0
            fi
        else
            # 精确IP匹配
            if [[ "$ip" == "$subnet" ]]; then
                echo "DEBUG [WHITELIST] IP $ip matched exact IP $subnet" >> "$DEBUG_LOG"
                return 0
            fi
        fi
    done
    return 1
}

# 分析失败日志
analyze_error_log() {
    # 检查错误日志文件是否存在且可读
    if [[ ! -r "$ERROR_LOG" ]]; then
        echo "DEBUG [ERROR] Cannot read error log: $ERROR_LOG" >> "$DEBUG_LOG"
        return 1
    fi

    local last_pos=$(get_last_position "$ERROR_LOG")
    local current_pos=$(get_current_size "$ERROR_LOG")

    # 如果文件被轮转，重置位置
    [ "$last_pos" -gt "$current_pos" ] && last_pos=0

    if [ "$current_pos" -gt "$last_pos" ]; then
        echo "DEBUG [ERROR_LOG] Processing from position $last_pos to $current_pos" >> "$DEBUG_LOG"

        tail -c +$((last_pos + 1)) "$ERROR_LOG" | grep -E "Access denied|ERROR 1045" | while read -r line; do
            local ip=""
            local ip_or_host=""

            # 更健壮的正则表达式匹配
            if [[ "$line" =~ Access\ denied\ for\ user\ .*@\'([^\']+)\' ]] || [[ "$line" =~ Access\ denied\ for\ user\ .*@([^\ ]+) ]]; then
                ip_or_host="${BASH_REMATCH[1]}"

                if [[ "$ip_or_host" == "localhost" ]]; then
                    ip="127.0.0.1"
                elif [[ "$ip_or_host" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                    ip="$ip_or_host"
                else
                    # 尝试解析主机名
                    ip=$(getent hosts "$ip_or_host" 2>/dev/null | awk '{print $1}' | head -1)
                    [ -z "$ip" ] && ip="$ip_or_host"
                fi
            fi

            echo "DEBUG [FAIL]: $line -> IP: $ip" >> "$DEBUG_LOG"
            [ -z "$ip" ] && continue
            is_whitelisted "$ip" && continue
            echo "$(date +%s) $ip failed" >> "$STATE_DIR/login_attempts.log"
        done
        record_position "$ERROR_LOG" "$current_pos"
    fi
}

# 分析成功日志
analyze_general_log() {
    # 检查通用日志文件是否存在且可读
    if [[ ! -r "$GENERAL_LOG" ]]; then
        echo "DEBUG [ERROR] Cannot read general log: $GENERAL_LOG" >> "$DEBUG_LOG"
        return 1
    fi

    local last_pos=$(get_last_position "$GENERAL_LOG")
    local current_pos=$(get_current_size "$GENERAL_LOG")

    # 如果文件被轮转，重置位置
    [ "$last_pos" -gt "$current_pos" ] && last_pos=0

    if [ "$current_pos" -gt "$last_pos" ]; then
        echo "DEBUG [GENERAL_LOG] Processing from position $last_pos to $current_pos" >> "$DEBUG_LOG"

        tail -c +$((last_pos + 1)) "$GENERAL_LOG" | grep "Connect" | while read -r line; do
            local ip=""
            local ip_or_host=""

            # 更健壮的IP提取
            if [[ "$line" =~ @([0-9]+\.[0-9]+\.[0-9]+\.[0-9]+) ]]; then
                ip="${BASH_REMATCH[1]}"
            elif [[ "$line" =~ @([a-zA-Z0-9\.\-]+) ]]; then
                ip_or_host="${BASH_REMATCH[1]}"
                # 尝试解析主机名
                ip=$(getent hosts "$ip_or_host" 2>/dev/null | awk '{print $1}' | head -1)
                [ -z "$ip" ] && ip="$ip_or_host"
            fi

            echo "DEBUG [SUCCESS]: $line -> IP: $ip" >> "$DEBUG_LOG"
            [ -z "$ip" ] && continue
            is_whitelisted "$ip" && continue
            echo "$(date +%s) $ip success" >> "$STATE_DIR/login_attempts.log"
        done
        record_position "$GENERAL_LOG" "$current_pos"
    fi
}

# 检测异常登录行为
detect_abnormal_logins() {
    local now=$(date +%s)
    local threshold=$((now - TIME_WINDOW))
    declare -A failed_counts success_counts
    local total_records=0
    local valid_records=0

    echo "DEBUG [TIME] Now: $now, Threshold: $threshold, Window: $TIME_WINDOW seconds" >> "$DEBUG_LOG"
    echo "DEBUG [TIME] Analyzing records newer than $(date -d @$threshold '+%F %T')" >> "$DEBUG_LOG"

    [ ! -f "$STATE_DIR/login_attempts.log" ] && {
        echo "DEBUG [DETECT] No login attempts log found" >> "$DEBUG_LOG"
        return
    }

    while read -r line; do
        ((total_records++))

        # 解析时间戳
        local timestamp=${line%% *}

        # 跳过时间窗口外的记录
        if [ "$timestamp" -lt "$threshold" ]; then
            echo "DEBUG [SKIP] Old record: $line (timestamp: $timestamp < $threshold)" >> "$DEBUG_LOG"
            continue
        fi

        ((valid_records++))

        # 解析IP和状态
        local rest=${line#* }
        local ip=${rest% *}
        local status=${line##* }

        echo "DEBUG [PARSE] Record: timestamp=$timestamp, ip=$ip, status=$status" >> "$DEBUG_LOG"

        # 统计计数
        if [[ "$status" == "failed" ]]; then
            ((failed_counts[$ip]++))
        elif [[ "$status" == "success" ]]; then
            ((success_counts[$ip]++))
        fi
    done < "$STATE_DIR/login_attempts.log"

    echo "DEBUG [STATS] Total records: $total_records, Valid records in window: $valid_records" >> "$DEBUG_LOG"

    # 报告失败登录
    for ip in "${!failed_counts[@]}"; do
        echo "DEBUG [COUNT] $ip failed: ${failed_counts[$ip]}, Threshold: $FAIL_THRESHOLD" >> "$DEBUG_LOG"
        if [ "${failed_counts[$ip]}" -ge "$FAIL_THRESHOLD" ]; then
            echo "$(date '+%F %T') [FAILED LOGIN] IP: $ip, Count: ${failed_counts[$ip]}, Threshold: $FAIL_THRESHOLD" >> "$EVENT_LOG"
        fi
    done

    # 报告成功登录
    for ip in "${!success_counts[@]}"; do
        echo "DEBUG [COUNT] $ip success: ${success_counts[$ip]}, Threshold: $SUCCESS_THRESHOLD" >> "$DEBUG_LOG"
        if [ "${success_counts[$ip]}" -ge "$SUCCESS_THRESHOLD" ]; then
            echo "$(date '+%F %T') [SUCCESS LOGIN] IP: $ip, Count: ${success_counts[$ip]}, Threshold: $SUCCESS_THRESHOLD" >> "$EVENT_LOG"
        fi
    done
}

# 清理过期记录
cleanup_old_records() {
    local now=$(date +%s)
    local cutoff=$((now - TIME_WINDOW * 3))  # 保留3倍时间窗口的数据，确保有足够历史数据

    [ ! -f "$STATE_DIR/login_attempts.log" ] && {
        echo "DEBUG [CLEANUP] No login attempts log to clean" >> "$DEBUG_LOG"
        return
    }

    local before_count=$(wc -l < "$STATE_DIR/login_attempts.log")
    echo "DEBUG [CLEANUP] Before cleanup: $before_count records, cutoff: $cutoff ($(date -d @$cutoff '+%F %T'))" >> "$DEBUG_LOG"

    # 使用临时文件安全地清理记录
    if awk -v cutoff="$cutoff" '$1 >= cutoff' "$STATE_DIR/login_attempts.log" > "$STATE_DIR/tmp_attempts.log"; then
        local after_count=$(wc -l < "$STATE_DIR/tmp_attempts.log")
        echo "DEBUG [CLEANUP] After cleanup: $after_count records, removed: $((before_count - after_count))" >> "$DEBUG_LOG"

        if mv "$STATE_DIR/tmp_attempts.log" "$STATE_DIR/login_attempts.log"; then
            echo "DEBUG [CLEANUP] Cleanup completed successfully" >> "$DEBUG_LOG"
        else
            echo "ERROR [CLEANUP] Failed to move cleaned file" >> "$DEBUG_LOG"
            rm -f "$STATE_DIR/tmp_attempts.log"
        fi
    else
        echo "ERROR [CLEANUP] Failed to clean records" >> "$DEBUG_LOG"
        rm -f "$STATE_DIR/tmp_attempts.log"
    fi
}

# 强制清理所有历史数据（一次性清理函数）
force_cleanup_all() {
    echo "DEBUG [FORCE_CLEANUP] Removing all historical data" >> "$DEBUG_LOG"
    > "$STATE_DIR/login_attempts.log"
    rm -f "$STATE_DIR"/*.pos
    echo "DEBUG [FORCE_CLEANUP] All historical data cleared" >> "$DEBUG_LOG"
}

main() {
    echo "===== $(date '+%F %T') MySQL Monitor Start =====" >> "$DEBUG_LOG"

    # 检查依赖
    check_dependencies

    # 创建目录
    create_directories

    # 如果传入参数 --force-cleanup，则强制清理所有历史数据
    if [[ "$1" == "--force-cleanup" ]]; then
        force_cleanup_all
        echo "Historical data cleared. Please run the script again without --force-cleanup parameter."
        exit 0
    fi

    analyze_error_log
    analyze_general_log
    detect_abnormal_logins
    cleanup_old_records

    echo "===== $(date '+%F %T') MySQL Monitor End =====" >> "$DEBUG_LOG"
}

main "$@"
